import sqlite3
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

class InventoryManagementSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة مخازن المقاولات")
        self.root.geometry("1000x700")
        
        # الاتصال بقاعدة البيانات
        self.conn = sqlite3.connect('construction_inventory.db')
        self.create_tables()
        
        # إنشاء واجهة المستخدم
        self.create_ui()
        
    def create_tables(self):
        # جدول الأصناف (Items)
        self.conn.execute('''
        CREATE TABLE IF NOT EXISTS items (
            item_id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_name TEXT NOT NULL UNIQUE,
            unit TEXT NOT NULL,
            available_qty REAL DEFAULT 0,
            min_level REAL DEFAULT 0,
            notes TEXT
        )
        ''')
        
        # جدول المشاريع (Projects)
        self.conn.execute('''
        CREATE TABLE IF NOT EXISTS projects (
            project_id INTEGER PRIMARY KEY AUTOINCREMENT,
            project_name TEXT NOT NULL UNIQUE,
            address TEXT,
            start_date TEXT,
            end_date TEXT,
            notes TEXT
        )
        ''')
        
        # جدول الحركات (Transactions)
        self.conn.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            transaction_id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_type TEXT NOT NULL,  # 'in' or 'out'
            item_id INTEGER NOT NULL,
            quantity REAL NOT NULL,
            project_id INTEGER,
            date TEXT NOT NULL,
            user TEXT,
            notes TEXT,
            FOREIGN KEY (item_id) REFERENCES items(item_id),
            FOREIGN KEY (project_id) REFERENCES projects(project_id)
        )
        ''')
        
        self.conn.commit()
    
    def create_ui(self):
        # إنشاء تبويبات الواجهة
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True)
        
        # تبويب الأصناف
        tab_items = ttk.Frame(notebook)
        notebook.add(tab_items, text='إدارة الأصناف')
        self.items_ui(tab_items)
        
        # تبويب المشاريع
        tab_projects = ttk.Frame(notebook)
        notebook.add(tab_projects, text='إدارة المشاريع')
        self.projects_ui(tab_projects)
        
        # تبويب الحركات
        tab_transactions = ttk.Frame(notebook)
        notebook.add(tab_transactions, text='حركات المخزن')
        self.transactions_ui(tab_transactions)
        
        # تبويب التقارير
        tab_reports = ttk.Frame(notebook)
        notebook.add(tab_reports, text='التقارير')
        self.reports_ui(tab_reports)
    
    def items_ui(self, parent):
        # إطار الإضافة والتعديل
        control_frame = ttk.LabelFrame(parent, text="إضافة/تعديل صنف")
        control_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(control_frame, text="اسم الصنف:").grid(row=0, column=0, padx=5, pady=5)
        self.entry_item_name = ttk.Entry(control_frame, width=30)
        self.entry_item_name.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(control_frame, text="الوحدة:").grid(row=0, column=2, padx=5, pady=5)
        self.entry_unit = ttk.Entry(control_frame, width=15)
        self.entry_unit.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Label(control_frame, text="الحد الأدنى:").grid(row=0, column=4, padx=5, pady=5)
        self.entry_min_level = ttk.Entry(control_frame, width=15)
        self.entry_min_level.grid(row=0, column=5, padx=5, pady=5)
        
        ttk.Label(control_frame, text="الملاحظات:").grid(row=1, column=0, padx=5, pady=5)
        self.entry_item_notes = ttk.Entry(control_frame, width=30)
        self.entry_item_notes.grid(row=1, column=1, padx=5, pady=5)
        
        btn_add = ttk.Button(control_frame, text="إضافة صنف", command=self.add_item)
        btn_add.grid(row=1, column=2, padx=5, pady=5)
        
        btn_edit = ttk.Button(control_frame, text="تعديل الصنف", command=self.edit_item)
        btn_edit.grid(row=1, column=3, padx=5, pady=5)
        
        btn_delete = ttk.Button(control_frame, text="حذف الصنف", command=self.delete_item)
        btn_delete.grid(row=1, column=4, padx=5, pady=5)
        
        # شجرة عرض الأصناف
        display_frame = ttk.LabelFrame(parent, text="قائمة الأصناف")
        display_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        columns = ('item_id', 'item_name', 'unit', 'available_qty', 'min_level', 'notes')
        self.tree_items = ttk.Treeview(display_frame, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة بالعربية
        self.tree_items.heading('item_id', text='كود الصنف')
        self.tree_items.heading('item_name', text='اسم الصنف')
        self.tree_items.heading('unit', text='الوحدة')
        self.tree_items.heading('available_qty', text='الكمية المتاحة')
        self.tree_items.heading('min_level', text='الحد الأدنى')
        self.tree_items.heading('notes', text='الملاحظات')
        
        for col in columns:
            self.tree_items.column(col, width=120)
        
        self.tree_items.pack(fill='both', expand=True)
        
        # حدث اختيار صنف من الشجرة
        self.tree_items.bind('<<TreeviewSelect>>', self.select_item)
        
        # تحديث قائمة الأصناف
        self.update_items_list()
    
    def update_items_list(self):
        # مسح البيانات الحالية
        for item in self.tree_items.get_children():
            self.tree_items.delete(item)
        
        # جلب البيانات من قاعدة البيانات
        items = self.conn.execute('SELECT * FROM items ORDER BY item_name').fetchall()
        
        # إضافة البيانات إلى الشجرة
        for item in items:
            self.tree_items.insert('', 'end', values=item)
    
    def select_item(self, event):
        selected = self.tree_items.selection()
        if not selected:
            return
        
        item = self.tree_items.item(selected[0])
        values = item['values']
        
        # تعبئة حقول التعديل
        self.entry_item_name.delete(0, 'end')
        self.entry_item_name.insert(0, values[1])
        
        self.entry_unit.delete(0, 'end')
        self.entry_unit.insert(0, values[2])
        
        self.entry_min_level.delete(0, 'end')
        self.entry_min_level.insert(0, values[4])
        
        self.entry_item_notes.delete(0, 'end')
        self.entry_item_notes.insert(0, values[5] if len(values) > 5 else '')
    
    def add_item(self):
        item_name = self.entry_item_name.get().strip()
        unit = self.entry_unit.get().strip()
        min_level = self.entry_min_level.get().strip()
        notes = self.entry_item_notes.get().strip()
        
        if not item_name or not unit:
            messagebox.showerror("خطأ", "يجب إدخال اسم الصنف والوحدة")
            return
        
        try:
            min_level = float(min_level) if min_level else 0
        except ValueError:
            messagebox.showerror("خطأ", "الحد الأدنى يجب أن يكون رقمًا")
            return
        
        try:
            self.conn.execute('''
            INSERT INTO items (item_name, unit, min_level, notes)
            VALUES (?, ?, ?, ?)
            ''', (item_name, unit, min_level, notes))
            self.conn.commit()
            messagebox.showinfo("نجاح", "تمت إضافة الصنف بنجاح")
            self.update_items_list()
            self.clear_item_fields()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم الصنف موجود مسبقًا")
    
    def edit_item(self):
        selected = self.tree_items.selection()
        if not selected:
            messagebox.showerror("خطأ", "يجب اختيار صنف للتعديل")
            return
        
        item = self.tree_items.item(selected[0])
        item_id = item['values'][0]
        
        item_name = self.entry_item_name.get().strip()
        unit = self.entry_unit.get().strip()
        min_level = self.entry_min_level.get().strip()
        notes = self.entry_item_notes.get().strip()
        
        if not item_name or not unit:
            messagebox.showerror("خطأ", "يجب إدخال اسم الصنف والوحدة")
            return
        
        try:
            min_level = float(min_level) if min_level else 0
        except ValueError:
            messagebox.showerror("خطأ", "الحد الأدنى يجب أن يكون رقمًا")
            return
        
        try:
            self.conn.execute('''
            UPDATE items 
            SET item_name=?, unit=?, min_level=?, notes=?
            WHERE item_id=?
            ''', (item_name, unit, min_level, notes, item_id))
            self.conn.commit()
            messagebox.showinfo("نجاح", "تم تعديل الصنف بنجاح")
            self.update_items_list()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم الصنف موجود مسبقًا")
    
    def delete_item(self):
        selected = self.tree_items.selection()
        if not selected:
            messagebox.showerror("خطأ", "يجب اختيار صنف للحذف")
            return
        
        if not messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا الصنف؟"):
            return
        
        item = self.tree_items.item(selected[0])
        item_id = item['values'][0]
        
        try:
            # التحقق من وجود حركات مرتبطة بالصنف
            transactions = self.conn.execute('SELECT COUNT(*) FROM transactions WHERE item_id=?', (item_id,)).fetchone()[0]
            if transactions > 0:
                messagebox.showerror("خطأ", "لا يمكن حذف الصنف لوجود حركات مرتبطة به")
                return
            
            self.conn.execute('DELETE FROM items WHERE item_id=?', (item_id,))
            self.conn.commit()
            messagebox.showinfo("نجاح", "تم حذف الصنف بنجاح")
            self.update_items_list()
            self.clear_item_fields()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف الصنف: {str(e)}")
    
    def clear_item_fields(self):
        self.entry_item_name.delete(0, 'end')
        self.entry_unit.delete(0, 'end')
        self.entry_min_level.delete(0, 'end')
        self.entry_item_notes.delete(0, 'end')
    
    # باقي الوظائف لواجهات المشاريع والحركات والتقارير
    def projects_ui(self, parent):
        frame_control = ttk.LabelFrame(parent, text="إضافة/تعديل مشروع")
        frame_control.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(frame_control, text="اسم المشروع:").grid(row=0, column=0, padx=5, pady=5)
        self.entry_project_name = ttk.Entry(frame_control, width=30)
        self.entry_project_name.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(frame_control, text="العنوان:").grid(row=0, column=2, padx=5, pady=5)
        self.entry_project_address = ttk.Entry(frame_control, width=30)
        self.entry_project_address.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Label(frame_control, text="تاريخ البدء:").grid(row=1, column=0, padx=5, pady=5)
        self.entry_start_date = ttk.Entry(frame_control, width=15)
        self.entry_start_date.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(frame_control, text="تاريخ الانتهاء:").grid(row=1, column=2, padx=5, pady=5)
        self.entry_end_date = ttk.Entry(frame_control, width=15)
        self.entry_end_date.grid(row=1, column=3, padx=5, pady=5)
        
        ttk.Label(frame_control, text="الملاحظات:").grid(row=2, column=0, padx=5, pady=5)
        self.entry_project_notes = ttk.Entry(frame_control, width=30)
        self.entry_project_notes.grid(row=2, column=1, columnspan=3, padx=5, pady=5, sticky='we')
        
        btn_add = ttk.Button(frame_control, text="إضافة مشروع", command=self.add_project)
        btn_add.grid(row=3, column=0, padx=5, pady=5)
        
        btn_edit = ttk.Button(frame_control, text="تعديل المشروع", command=self.edit_project)
        btn_edit.grid(row=3, column=1, padx=5, pady=5)
        
        btn_delete = ttk.Button(frame_control, text="حذف المشروع", command=self.delete_project)
        btn_delete.grid(row=3, column=2, padx=5, pady=5)
        
        # شجرة عرض المشاريع
        frame_display = ttk.LabelFrame(parent, text="قائمة المشاريع")
        frame_display.pack(fill='both', expand=True, padx=10, pady=5)
        
        columns = ('project_id', 'project_name', 'address', 'start_date', 'end_date', 'notes')
        self.tree_projects = ttk.Treeview(frame_display, columns=columns, show='headings', height=10)
        
        # تعيين عناوين الأعمدة بالعربية
        self.tree_projects.heading('project_id', text='كود المشروع')
        self.tree_projects.heading('project_name', text='اسم المشروع')
        self.tree_projects.heading('address', text='العنوان')
        self.tree_projects.heading('start_date', text='تاريخ البدء')
        self.tree_projects.heading('end_date', text='تاريخ الانتهاء')
        self.tree_projects.heading('notes', text='الملاحظات')
        
        for col in columns:
            self.tree_projects.column(col, width=120)
        
        self.tree_projects.pack(fill='both', expand=True)
        
        # حدث اختيار مشروع من الشجرة
        self.tree_projects.bind('<<TreeviewSelect>>', self.select_project)
        
        # تحديث قائمة المشاريع
        self.update_projects_list()
    
    def update_projects_list(self):
        for item in self.tree_projects.get_children():
            self.tree_projects.delete(item)
        
        projects = self.conn.execute('SELECT * FROM projects ORDER BY project_name').fetchall()
        
        for project in projects:
            self.tree_projects.insert('', 'end', values=project)
    
    def select_project(self, event):
        selected = self.tree_projects.selection()
        if not selected:
            return
        
        item = self.tree_projects.item(selected[0])
        values = item['values']
        
        self.entry_project_name.delete(0, 'end')
        self.entry_project_name.insert(0, values[1])
        
        self.entry_project_address.delete(0, 'end')
        self.entry_project_address.insert(0, values[2])
        
        self.entry_start_date.delete(0, 'end')
        self.entry_start_date.insert(0, values[3])
        
        self.entry_end_date.delete(0, 'end')
        self.entry_end_date.insert(0, values[4])
        
        self.entry_project_notes.delete(0, 'end')
        self.entry_project_notes.insert(0, values[5] if len(values) > 5 else '')
    
    def add_project(self):
        project_name = self.entry_project_name.get().strip()
        address = self.entry_project_address.get().strip()
        start_date = self.entry_start_date.get().strip()
        end_date = self.entry_end_date.get().strip()
        notes = self.entry_project_notes.get().strip()
        
        if not project_name:
            messagebox.showerror("خطأ", "يجب إدخال اسم المشروع")
            return
        
        try:
            self.conn.execute('''
            INSERT INTO projects (project_name, address, start_date, end_date, notes)
            VALUES (?, ?, ?, ?, ?)
            ''', (project_name, address, start_date, end_date, notes))
            self.conn.commit()
            messagebox.showinfo("نجاح", "تمت إضافة المشروع بنجاح")
            self.update_projects_list()
            self.clear_project_fields()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المشروع موجود مسبقًا")
    
    def edit_project(self):
        selected = self.tree_projects.selection()
        if not selected:
            messagebox.showerror("خطأ", "يجب اختيار مشروع للتعديل")
            return
        
        item = self.tree_projects.item(selected[0])
        project_id = item['values'][0]
        
        project_name = self.entry_project_name.get().strip()
        address = self.entry_project_address.get().strip()
        start_date = self.entry_start_date.get().strip()
        end_date = self.entry_end_date.get().strip()
        notes = self.entry_project_notes.get().strip()
        
        if not project_name:
            messagebox.showerror("خطأ", "يجب إدخال اسم المشروع")
            return
        
        try:
            self.conn.execute('''
            UPDATE projects 
            SET project_name=?, address=?, start_date=?, end_date=?, notes=?
            WHERE project_id=?
            ''', (project_name, address, start_date, end_date, notes, project_id))
            self.conn.commit()
            messagebox.showinfo("نجاح", "تم تعديل المشروع بنجاح")
            self.update_projects_list()
        except sqlite3.IntegrityError:
            messagebox.showerror("خطأ", "اسم المشروع موجود مسبقًا")
    
    def delete_project(self):
        selected = self.tree_projects.selection()
        if not selected:
            messagebox.showerror("خطأ", "يجب اختيار مشروع للحذف")
            return
        
        if not messagebox.askyesno("تأكيد", "هل أنت متأكد من حذف هذا المشروع؟"):
            return
        
        item = self.tree_projects.item(selected[0])
        project_id = item['values'][0]
        
        try:
            # التحقق من وجود حركات مرتبطة بالمشروع
            transactions = self.conn.execute('SELECT COUNT(*) FROM transactions WHERE project_id=?', (project_id,)).fetchone()[0]
            if transactions > 0:
                messagebox.showerror("خطأ", "لا يمكن حذف المشروع لوجود حركات مرتبطة به")
                return
            
            self.conn.execute('DELETE FROM projects WHERE project_id=?', (project_id,))
            self.conn.commit()
            messagebox.showinfo("نجاح", "تم حذف المشروع بنجاح")
            self.update_projects_list()
            self.clear_project_fields()
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المشروع: {str(e)}")
    
    def clear_project_fields(self):
        self.entry_project_name.delete(0, 'end')
        self.entry_project_address.delete(0, 'end')
        self.entry_start_date.delete(0, 'end')
        self.entry_end_date.delete(0, 'end')
        self.entry_project_notes.delete(0, 'end')
    
    def transactions_ui(self, parent):
        frame_control = ttk.LabelFrame(parent, text="تسجيل حركة")
        frame_control.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(frame_control, text="نوع الحركة:").grid(row=0, column=0, padx=5, pady=5)
        self.cmb_trans_type = ttk.Combobox(frame_control, values=['دخول', 'خروج'], state='readonly')
        self.cmb_trans_type.grid(row=0, column=1, padx=5, pady=5)
        self.cmb_trans_type.current(0)
        
        ttk.Label(frame_control, text="الصنف:").grid(row=0, column=2, padx=5, pady=5)
        self.cmb_item = ttk.Combobox(frame_control, state='readonly')
        self.cmb_item.grid(row=0, column=3, padx=5, pady=5)
        self.update_items_combobox()
        
        ttk.Label(frame_control, text="الكمية:").grid(row=1, column=0, padx=5, pady=5)
        self.entry_quantity = ttk.Entry(frame_control, width=15)
        self.entry_quantity.grid(row=1, column=1, padx=5, pady=5)
        
        ttk.Label(frame_control, text="المشروع:").grid(row=1, column=2, padx=5, pady=5)
        self.cmb_project = ttk.Combobox(frame_control, state='readonly')
        self.cmb_project.grid(row=1, column=3, padx=5, pady=5)
        self.update_projects_combobox()
        
        ttk.Label(frame_control, text="الملاحظات:").grid(row=2, column=0, padx=5, pady=5)
        self.entry_trans_notes = ttk.Entry(frame_control, width=30)
        self.entry_trans_notes.grid(row=2, column=1, columnspan=3, padx=5, pady=5, sticky='we')
        
        btn_save = ttk.Button(frame_control, text="حفظ الحركة", command=self.save_transaction)
        btn_save.grid(row=3, column=1, padx=5, pady=5)
        
        # شجرة عرض الحركات
        frame_display = ttk.LabelFrame(parent, text="سجل الحركات")
        frame_display.pack(fill='both', expand=True, padx=10, pady=5)
        
        columns = ('transaction_id', 'transaction_type', 'item_name', 'quantity', 'project_name', 'date', 'notes')
        self.tree_transactions = ttk.Treeview(frame_display, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة بالعربية
        self.tree_transactions.heading('transaction_id', text='رقم الحركة')
        self.tree_transactions.heading('transaction_type', text='نوع الحركة')
        self.tree_transactions.heading('item_name', text='اسم الصنف')
        self.tree_transactions.heading('quantity', text='الكمية')
        self.tree_transactions.heading('project_name', text='المشروع')
        self.tree_transactions.heading('date', text='التاريخ')
        self.tree_transactions.heading('notes', text='الملاحظات')
        
        for col in columns:
            self.tree_transactions.column(col, width=120)
        
        self.tree_transactions.pack(fill='both', expand=True)
        
        # تحديث قائمة الحركات
        self.update_transactions_list()
    
    def update_items_combobox(self):
        items = self.conn.execute('SELECT item_id, item_name FROM items ORDER BY item_name').fetchall()
        self.items_dict = {name: id for id, name in items}
        self.cmb_item['values'] = list(self.items_dict.keys())
        if self.items_dict:
            self.cmb_item.current(0)
    
    def update_projects_combobox(self):
        projects = self.conn.execute('SELECT project_id, project_name FROM projects ORDER BY project_name').fetchall()
        self.projects_dict = {name: id for id, name in projects}
        self.cmb_project['values'] = list(self.projects_dict.keys()) + ['بدون مشروع']
        self.cmb_project.current(0)
    
    def save_transaction(self):
        trans_type = 'in' if self.cmb_trans_type.get() == 'دخول' else 'out'
        item_name = self.cmb_item.get()
        quantity = self.entry_quantity.get().strip()
        project_name = self.cmb_project.get()
        notes = self.entry_trans_notes.get().strip()
        
        if not item_name:
            messagebox.showerror("خطأ", "يجب اختيار صنف")
            return
        
        if not quantity:
            messagebox.showerror("خطأ", "يجب إدخال الكمية")
            return
        
        try:
            quantity = float(quantity)
            if quantity <= 0:
                raise ValueError
        except ValueError:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون رقمًا موجبًا")
            return
        
        item_id = self.items_dict.get(item_name)
        project_id = self.projects_dict.get(project_name) if project_name != 'بدون مشروع' else None
        
        # التحقق من الكمية المتاحة في حالة حركة خروج
        if trans_type == 'out':
            available_qty = self.conn.execute('''
            SELECT available_qty FROM items WHERE item_id=?
            ''', (item_id,)).fetchone()[0]
            
            if available_qty < quantity:
                messagebox.showerror("خطأ", f"الكمية غير متاحة! الكمية المتاحة: {available_qty}")
                return
        
        date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        user = "admin"  # يمكن استبدالها بنظام مستخدمين
        
        try:
            # تسجيل الحركة
            self.conn.execute('''
            INSERT INTO transactions (transaction_type, item_id, quantity, project_id, date, user, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (trans_type, item_id, quantity, project_id, date, user, notes))
            
            # تحديث كمية الصنف المتاحة
            if trans_type == 'in':
                self.conn.execute('''
                UPDATE items 
                SET available_qty = available_qty + ?
                WHERE item_id = ?
                ''', (quantity, item_id))
            else:
                self.conn.execute('''
                UPDATE items 
                SET available_qty = available_qty - ?
                WHERE item_id = ?
                ''', (quantity, item_id))
            
            self.conn.commit()
            messagebox.showinfo("نجاح", "تم تسجيل الحركة بنجاح")
            self.update_transactions_list()
            self.clear_transaction_fields()
            self.update_items_list()  # لتحديث الكميات في شجرة الأصناف
        except Exception as e:
            self.conn.rollback()
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تسجيل الحركة: {str(e)}")
    
    def clear_transaction_fields(self):
        self.entry_quantity.delete(0, 'end')
        self.entry_trans_notes.delete(0, 'end')
    
    def update_transactions_list(self):
        for item in self.tree_transactions.get_children():
            self.tree_transactions.delete(item)
        
        transactions = self.conn.execute('''
        SELECT t.transaction_id, 
               CASE WHEN t.transaction_type = 'in' THEN 'دخول' ELSE 'خروج' END,
               i.item_name, t.quantity, 
               COALESCE(p.project_name, 'بدون مشروع'), t.date, t.notes
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.item_id
        LEFT JOIN projects p ON t.project_id = p.project_id
        ORDER BY t.transaction_id DESC
        LIMIT 100
        ''').fetchall()
        
        for trans in transactions:
            self.tree_transactions.insert('', 'end', values=trans)
    
    def reports_ui(self, parent):
        frame_control = ttk.LabelFrame(parent, text="خيارات التقرير")
        frame_control.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(frame_control, text="من تاريخ:").grid(row=0, column=0, padx=5, pady=5)
        self.entry_from_date = ttk.Entry(frame_control, width=15)
        self.entry_from_date.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(frame_control, text="إلى تاريخ:").grid(row=0, column=2, padx=5, pady=5)
        self.entry_to_date = ttk.Entry(frame_control, width=15)
        self.entry_to_date.grid(row=0, column=3, padx=5, pady=5)
        
        ttk.Label(frame_control, text="الصنف:").grid(row=1, column=0, padx=5, pady=5)
        self.cmb_report_item = ttk.Combobox(frame_control, state='readonly')
        self.cmb_report_item.grid(row=1, column=1, padx=5, pady=5)
        self.update_report_items()
        
        ttk.Label(frame_control, text="المشروع:").grid(row=1, column=2, padx=5, pady=5)
        self.cmb_report_project = ttk.Combobox(frame_control, state='readonly')
        self.cmb_report_project.grid(row=1, column=3, padx=5, pady=5)
        self.update_report_projects()
        
        btn_show = ttk.Button(frame_control, text="عرض التقرير", command=self.show_report)
        btn_show.grid(row=2, column=1, columnspan=2, padx=5, pady=5)
        
        # شجرة عرض التقارير
        frame_display = ttk.LabelFrame(parent, text="نتيجة التقرير")
        frame_display.pack(fill='both', expand=True, padx=10, pady=5)
        
        columns = ('transaction_type', 'item_name', 'quantity', 'project_name', 'date', 'notes')
        self.tree_reports = ttk.Treeview(frame_display, columns=columns, show='headings', height=15)
        
        # تعيين عناوين الأعمدة بالعربية
        self.tree_reports.heading('transaction_type', text='نوع الحركة')
        self.tree_reports.heading('item_name', text='اسم الصنف')
        self.tree_reports.heading('quantity', text='الكمية')
        self.tree_reports.heading('project_name', text='المشروع')
        self.tree_reports.heading('date', text='التاريخ')
        self.tree_reports.heading('notes', text='الملاحظات')
        
        for col in columns:
            self.tree_reports.column(col, width=120)
        
        self.tree_reports.pack(fill='both', expand=True)
    
    def update_report_items(self):
        items = self.conn.execute('SELECT item_id, item_name FROM items ORDER BY item_name').fetchall()
        self.report_items_dict = {name: id for id, name in items}
        self.cmb_report_item['values'] = ['كل الأصناف'] + list(self.report_items_dict.keys())
        self.cmb_report_item.current(0)
    
    def update_report_projects(self):
        projects = self.conn.execute('SELECT project_id, project_name FROM projects ORDER BY project_name').fetchall()
        self.report_projects_dict = {name: id for id, name in projects}
        self.cmb_report_project['values'] = ['كل المشاريع'] + list(self.report_projects_dict.keys())
        self.cmb_report_project.current(0)
    
    def show_report(self):
        from_date = self.entry_from_date.get().strip()
        to_date = self.entry_to_date.get().strip()
        item_name = self.cmb_report_item.get()
        project_name = self.cmb_report_project.get()
        
        # بناء الاستعلام
        query = '''
        SELECT CASE WHEN t.transaction_type = 'in' THEN 'دخول' ELSE 'خروج' END,
               i.item_name, t.quantity, 
               COALESCE(p.project_name, 'بدون مشروع'), t.date, t.notes
        FROM transactions t
        LEFT JOIN items i ON t.item_id = i.item_id
        LEFT JOIN projects p ON t.project_id = p.project_id
        WHERE 1=1
        '''
        params = []
        
        if from_date:
            query += ' AND t.date >= ?'
            params.append(from_date)
        
        if to_date:
            query += ' AND t.date <= ?'
            params.append(to_date)
        
        if item_name and item_name != 'كل الأصناف':
            query += ' AND i.item_name = ?'
            params.append(item_name)
        
        if project_name and project_name != 'كل المشاريع':
            query += ' AND p.project_name = ?'
            params.append(project_name)
        
        query += ' ORDER BY t.transaction_id DESC'
        
        # تنفيذ الاستعلام وعرض النتائج
        for item in self.tree_reports.get_children():
            self.tree_reports.delete(item)
        
        try:
            results = self.conn.execute(query, params).fetchall()
            for result in results:
                self.tree_reports.insert('', 'end', values=result)
            
            messagebox.showinfo("نجاح", f"تم عرض {len(results)} سجل")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عرض التقرير: {str(e)}")
    
    def __del__(self):
        self.conn.close()

if __name__ == '__main__':
    root = tk.Tk()
    app = InventoryManagementSystem(root)
    root.mainloop()